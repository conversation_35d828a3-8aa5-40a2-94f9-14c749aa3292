{"id": "7b1dedf8-2886-4059-b9af-0a9755e91037", "prevId": "********-0000-0000-0000-************", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "providerAccountId": {"name": "providerAccountId", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_userId_users_id_fk": {"name": "accounts_userId_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.employees": {"name": "employees", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.proposal_templates": {"name": "proposal_templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "cover_page_title": {"name": "cover_page_title", "type": "text", "primaryKey": false, "notNull": true}, "cover_page_subtitle": {"name": "cover_page_subtitle", "type": "text", "primaryKey": false, "notNull": false}, "cover_page_image_url": {"name": "cover_page_image_url", "type": "text", "primaryKey": false, "notNull": false}, "about_us_mission": {"name": "about_us_mission", "type": "text", "primaryKey": false, "notNull": true}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#3B82F6'"}, "secondary_color": {"name": "secondary_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#1E40AF'"}, "accent_color": {"name": "accent_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#F59E0B'"}, "text_color": {"name": "text_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#1F2937'"}, "background_color": {"name": "background_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#FFFFFF'"}, "heading_font": {"name": "heading_font", "type": "text", "primaryKey": false, "notNull": true, "default": "'Montserrat, sans-serif'"}, "body_font": {"name": "body_font", "type": "text", "primaryKey": false, "notNull": true, "default": "'Open Sans, sans-serif'"}, "font_size_base": {"name": "font_size_base", "type": "integer", "primaryKey": false, "notNull": true, "default": 12}, "line_height": {"name": "line_height", "type": "text", "primaryKey": false, "notNull": true, "default": "'1.5'"}, "page_margin": {"name": "page_margin", "type": "integer", "primaryKey": false, "notNull": true, "default": 20}, "section_spacing": {"name": "section_spacing", "type": "integer", "primaryKey": false, "notNull": true, "default": 15}, "header_height": {"name": "header_height", "type": "integer", "primaryKey": false, "notNull": true, "default": 80}, "footer_height": {"name": "footer_height", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "include_cover_page": {"name": "include_cover_page", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_about_page": {"name": "include_about_page", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_event_details": {"name": "include_event_details", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_speaker_profiles": {"name": "include_speaker_profiles", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_investment_summary": {"name": "include_investment_summary", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_thank_you_page": {"name": "include_thank_you_page", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_speaker_images": {"name": "show_speaker_images", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_speaker_bios": {"name": "show_speaker_bios", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_speaker_rates": {"name": "show_speaker_rates", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_company_logo": {"name": "show_company_logo", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "watermark_text": {"name": "watermark_text", "type": "text", "primaryKey": false, "notNull": false}, "layout_style": {"name": "layout_style", "type": "layout_style", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'modern'"}, "speaker_layout": {"name": "speaker_layout", "type": "speaker_layout", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'cards'"}, "image_style": {"name": "image_style", "type": "image_style", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'rounded'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"proposal_templates_user_id_users_id_fk": {"name": "proposal_templates_user_id_users_id_fk", "tableFrom": "proposal_templates", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.proposals": {"name": "proposals", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_name": {"name": "event_name", "type": "text", "primaryKey": false, "notNull": false}, "speaker_id": {"name": "speaker_id", "type": "uuid", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "pdf_path": {"name": "pdf_path", "type": "text", "primaryKey": false, "notNull": false}, "submitted_date": {"name": "submitted_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"proposals_speaker_id_speakers_id_fk": {"name": "proposals_speaker_id_speakers_id_fk", "tableFrom": "proposals", "tableTo": "speakers", "columnsFrom": ["speaker_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "sessionToken": {"name": "sessionToken", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "uuid", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_userId_users_id_fk": {"name": "sessions_userId_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_sessionToken_unique": {"name": "sessions_sessionToken_unique", "nullsNotDistinct": false, "columns": ["sessionToken"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.speaker_images": {"name": "speaker_images", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "speaker_id": {"name": "speaker_id", "type": "uuid", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"speaker_images_speaker_id_speakers_id_fk": {"name": "speaker_images_speaker_id_speakers_id_fk", "tableFrom": "speaker_images", "tableTo": "speakers", "columnsFrom": ["speaker_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.speakers": {"name": "speakers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "rate": {"name": "rate", "type": "integer", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "experience": {"name": "experience", "type": "text", "primaryKey": false, "notNull": false}, "availability": {"name": "availability", "type": "availability", "typeSchema": "public", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "emailVerified": {"name": "emailVerified", "type": "timestamp", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verificationToken": {"name": "verificationToken", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verificationToken_identifier_token_pk": {"name": "verificationToken_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.availability": {"name": "availability", "schema": "public", "values": ["Available", "Busy", "Unavailable"]}, "public.image_style": {"name": "image_style", "schema": "public", "values": ["square", "circle", "rounded"]}, "public.layout_style": {"name": "layout_style", "schema": "public", "values": ["classic", "modern", "minimal", "creative"]}, "public.speaker_layout": {"name": "speaker_layout", "schema": "public", "values": ["grid", "list", "cards"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}