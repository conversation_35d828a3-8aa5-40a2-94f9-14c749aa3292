import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import * as schema from './schema';

// Handle both browser and Node.js environments
const getDatabaseUrl = () => {
    // Browser environment
    if (typeof window !== 'undefined') {
        return import.meta.env.VITE_DATABASE_URL || import.meta.env.DATABASE_URL;
    }

    // Node.js environment
    if (typeof process !== 'undefined' && process.env) {
        return process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;
    }

    throw new Error('DATABASE_URL not found. Please set VITE_DATABASE_URL or DATABASE_URL environment variable.');
};

const databaseUrl = getDatabaseUrl();

if (!databaseUrl) {
    throw new Error('DATABASE_URL not found. Please set VITE_DATABASE_URL or DATABASE_URL environment variable.');
}

const sql = neon(databaseUrl);
export const db = drizzle(sql, { schema });

// Export schema for use in other parts of the application
export * from './schema';
