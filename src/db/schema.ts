import { pgTable, uuid, text, timestamp, integer, boolean, jsonb, pgEnum, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Enums
export const availabilityEnum = pgEnum('availability', ['Available', 'Busy', 'Unavailable']);
export const layoutStyleEnum = pgEnum('layout_style', ['classic', 'modern', 'minimal', 'creative']);
export const speakerLayoutEnum = pgEnum('speaker_layout', ['grid', 'list', 'cards']);
export const imageStyleEnum = pgEnum('image_style', ['square', 'circle', 'rounded']);

// Base tables
export const users = pgTable('users', {
    id: uuid('id').primaryKey().defaultRandom(),
    email: text('email').notNull().unique(),
    name: text('name'),
    image: text('image'),
    emailVerified: timestamp('emailVerified', { mode: 'date' }),
    createdAt: timestamp('createdAt', { mode: 'date' }).defaultNow(),
    updatedAt: timestamp('updatedAt', { mode: 'date' }).defaultNow(),
});

export const accounts = pgTable('accounts', {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
    type: text('type').notNull(),
    provider: text('provider').notNull(),
    providerAccountId: text('providerAccountId').notNull(),
    refresh_token: text('refresh_token'),
    access_token: text('access_token'),
    expires_at: integer('expires_at'),
    token_type: text('token_type'),
    scope: text('scope'),
    id_token: text('id_token'),
    session_state: text('session_state'),
});

export const sessions = pgTable('sessions', {
    id: uuid('id').primaryKey().defaultRandom(),
    sessionToken: text('sessionToken').notNull().unique(),
    userId: uuid('userId').notNull().references(() => users.id, { onDelete: 'cascade' }),
    expires: timestamp('expires', { mode: 'date' }).notNull(),
});

export const verificationTokens = pgTable('verificationToken', {
    identifier: text('identifier').notNull(),
    token: text('token').notNull(),
    expires: timestamp('expires', { mode: 'date' }).notNull(),
}, (vt) => ({
    compoundKey: primaryKey(vt.identifier, vt.token),
}));

// Application tables
export const employees = pgTable('employees', {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    email: text('email').notNull(),
    role: text('role'),
    phone: text('phone'),
    imageUrl: text('image_url'),
    createdAt: timestamp('created_at').defaultNow(),
});

export const speakers = pgTable('speakers', {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    bio: text('bio'),
    category: text('category'),
    rate: integer('rate'),
    location: text('location'),
    experience: text('experience'),
    availability: availabilityEnum('availability'),
    createdAt: timestamp('created_at').defaultNow(),
});

export const proposals = pgTable('proposals', {
    id: uuid('id').primaryKey().defaultRandom(),
    eventName: text('event_name'),
    speakerId: uuid('speaker_id').references(() => speakers.id),
    details: jsonb('details'),
    status: text('status'),
    pdfPath: text('pdf_path'),
    submittedDate: timestamp('submitted_date'),
    createdAt: timestamp('created_at').defaultNow(),
});

export const proposalTemplates = pgTable('proposal_templates', {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
    name: text('name').notNull(),
    coverPageTitle: text('cover_page_title').notNull(),
    coverPageSubtitle: text('cover_page_subtitle'),
    coverPageImageUrl: text('cover_page_image_url'),
    aboutUsMission: text('about_us_mission').notNull(),

    // Colors
    primaryColor: text('primary_color').notNull().default('#3B82F6'),
    secondaryColor: text('secondary_color').notNull().default('#1E40AF'),
    accentColor: text('accent_color').notNull().default('#F59E0B'),
    textColor: text('text_color').notNull().default('#1F2937'),
    backgroundColor: text('background_color').notNull().default('#FFFFFF'),

    // Typography
    headingFont: text('heading_font').notNull().default('Montserrat, sans-serif'),
    bodyFont: text('body_font').notNull().default('Open Sans, sans-serif'),
    fontSizeBase: integer('font_size_base').notNull().default(12),
    lineHeight: text('line_height').notNull().default('1.5'),

    // Layout
    pageMargin: integer('page_margin').notNull().default(20),
    sectionSpacing: integer('section_spacing').notNull().default(15),
    headerHeight: integer('header_height').notNull().default(80),
    footerHeight: integer('footer_height').notNull().default(50),

    // Page Structure
    includeCoverPage: boolean('include_cover_page').notNull().default(true),
    includeAboutPage: boolean('include_about_page').notNull().default(true),
    includeEventDetails: boolean('include_event_details').notNull().default(true),
    includeSpeakerProfiles: boolean('include_speaker_profiles').notNull().default(true),
    includeInvestmentSummary: boolean('include_investment_summary').notNull().default(true),
    includeThankYouPage: boolean('include_thank_you_page').notNull().default(true),

    // Content Options
    showSpeakerImages: boolean('show_speaker_images').notNull().default(true),
    showSpeakerBios: boolean('show_speaker_bios').notNull().default(true),
    showSpeakerRates: boolean('show_speaker_rates').notNull().default(true),
    showCompanyLogo: boolean('show_company_logo').notNull().default(true),
    watermarkText: text('watermark_text'),

    // Advanced Layout
    layoutStyle: layoutStyleEnum('layout_style').notNull().default('modern'),
    speakerLayout: speakerLayoutEnum('speaker_layout').notNull().default('cards'),
    imageStyle: imageStyleEnum('image_style').notNull().default('rounded'),

    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
});

export const categories = pgTable('categories', {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    createdAt: timestamp('created_at').defaultNow(),
});



export const speakerImages = pgTable('speaker_images', {
    id: uuid('id').primaryKey().defaultRandom(),
    speakerId: uuid('speaker_id').notNull().references(() => speakers.id, { onDelete: 'cascade' }),
    imageUrl: text('image_url').notNull(),
    createdAt: timestamp('created_at').defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
    accounts: many(accounts),
    sessions: many(sessions),
    proposalTemplates: many(proposalTemplates),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
    user: one(users, {
        fields: [accounts.userId],
        references: [users.id],
    }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
    user: one(users, {
        fields: [sessions.userId],
        references: [users.id],
    }),
}));

export const speakersRelations = relations(speakers, ({ many }) => ({
    proposals: many(proposals),
    speakerImages: many(speakerImages),
}));

export const proposalsRelations = relations(proposals, ({ one }) => ({
    speaker: one(speakers, {
        fields: [proposals.speakerId],
        references: [speakers.id],
    }),
}));

export const proposalTemplatesRelations = relations(proposalTemplates, ({ one }) => ({
    user: one(users, {
        fields: [proposalTemplates.userId],
        references: [users.id],
    }),
}));

export const speakerImagesRelations = relations(speakerImages, ({ one }) => ({
    speaker: one(speakers, {
        fields: [speakerImages.speakerId],
        references: [speakers.id],
    }),
}));
