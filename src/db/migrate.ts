import { db } from "./index";
import {
  users,
  accounts,
  sessions,
  verificationTokens,
  employees,
  speakers,
  proposals,
  proposalTemplates,
  categories,
  specialties,
  speakerImages
} from "./schema";

export async function migrate() {
  try {
    console.log("Starting database migration...");

    // Create tables in order (respecting foreign key dependencies)
    console.log("Creating users table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email TEXT NOT NULL UNIQUE,
        name TEXT,
        image TEXT,
        "emailVerified" TIMESTAMP,
        "createdAt" TIMESTAMP DEFAULT NOW(),
        "updatedAt" TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating accounts table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS accounts (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type TEXT NOT NULL,
        provider TEXT NOT NULL,
        "providerAccountId" TEXT NOT NULL,
        refresh_token TEXT,
        access_token TEXT,
        expires_at INTEGER,
        token_type TEXT,
        scope TEXT,
        id_token TEXT,
        session_state TEXT
      );
    `);

    console.log("Creating sessions table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS sessions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        "sessionToken" TEXT NOT NULL UNIQUE,
        "userId" UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        expires TIMESTAMP NOT NULL
      );
    `);

    console.log("Creating verificationToken table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS "verificationToken" (
        identifier TEXT NOT NULL,
        token TEXT NOT NULL,
        expires TIMESTAMP NOT NULL,
        PRIMARY KEY (identifier, token)
      );
    `);

    console.log("Creating employees table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS employees (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        role TEXT,
        phone TEXT,
        image_url TEXT,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating speakers table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS speakers (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        bio TEXT,
        category TEXT,
        image TEXT,
        rate INTEGER,
        email TEXT,
        phone TEXT,
        location TEXT,
        experience TEXT,
        specialties TEXT[],
        availability TEXT CHECK (availability IN ('Available', 'Busy', 'Unavailable')),
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating events table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS events (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title TEXT NOT NULL,
        description TEXT,
        date TIMESTAMP NOT NULL,
        "startTime" TEXT,
        "endTime" TEXT,
        location TEXT,
        status TEXT,
        "speakerIds" TEXT[],
        responsible_person_id UUID REFERENCES employees(id),
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating proposals table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS proposals (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        event_name TEXT,
        speaker_id UUID REFERENCES speakers(id),
        details JSONB,
        status TEXT,
        pdf_path TEXT,
        submitted_date TIMESTAMP,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating proposal_templates table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS proposal_templates (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name TEXT NOT NULL,
        cover_page_title TEXT NOT NULL,
        cover_page_subtitle TEXT,
        cover_page_image_url TEXT,
        about_us_mission TEXT NOT NULL,
        primary_color TEXT NOT NULL DEFAULT '#3B82F6',
        secondary_color TEXT NOT NULL DEFAULT '#1E40AF',
        accent_color TEXT NOT NULL DEFAULT '#F59E0B',
        text_color TEXT NOT NULL DEFAULT '#1F2937',
        background_color TEXT NOT NULL DEFAULT '#FFFFFF',
        heading_font TEXT NOT NULL DEFAULT 'Montserrat, sans-serif',
        body_font TEXT NOT NULL DEFAULT 'Open Sans, sans-serif',
        font_size_base INTEGER NOT NULL DEFAULT 12,
        line_height TEXT NOT NULL DEFAULT '1.5',
        page_margin INTEGER NOT NULL DEFAULT 20,
        section_spacing INTEGER NOT NULL DEFAULT 15,
        header_height INTEGER NOT NULL DEFAULT 80,
        footer_height INTEGER NOT NULL DEFAULT 50,
        include_cover_page BOOLEAN NOT NULL DEFAULT true,
        include_about_page BOOLEAN NOT NULL DEFAULT true,
        include_event_details BOOLEAN NOT NULL DEFAULT true,
        include_speaker_profiles BOOLEAN NOT NULL DEFAULT true,
        include_investment_summary BOOLEAN NOT NULL DEFAULT true,
        include_thank_you_page BOOLEAN NOT NULL DEFAULT true,
        show_speaker_images BOOLEAN NOT NULL DEFAULT true,
        show_speaker_bios BOOLEAN NOT NULL DEFAULT true,
        show_speaker_rates BOOLEAN NOT NULL DEFAULT true,
        show_company_logo BOOLEAN NOT NULL DEFAULT true,
        watermark_text TEXT,
        layout_style TEXT NOT NULL DEFAULT 'modern' CHECK (layout_style IN ('classic', 'modern', 'minimal', 'creative')),
        speaker_layout TEXT NOT NULL DEFAULT 'cards' CHECK (speaker_layout IN ('grid', 'list', 'cards')),
        image_style TEXT NOT NULL DEFAULT 'rounded' CHECK (image_style IN ('square', 'circle', 'rounded')),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating categories table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating specialties table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS specialties (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating bitrix_deals table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS bitrix_deals (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        bitrix_deal_id TEXT NOT NULL,
        deal_title TEXT NOT NULL,
        deal_amount INTEGER,
        deal_stage TEXT,
        client_name TEXT,
        client_email TEXT,
        event_date TIMESTAMP,
        event_location TEXT,
        speaker_requirements TEXT,
        sync_status TEXT,
        synced_to_event_id UUID REFERENCES events(id),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating email_settings table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS email_settings (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        stamp_mailbox TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
    `);

    console.log("Creating speaker_images table...");
    await db.execute(`
      CREATE TABLE IF NOT EXISTS speaker_images (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        speaker_id UUID NOT NULL REFERENCES speakers(id) ON DELETE CASCADE,
        image_url TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);

    // Insert default user for testing
    console.log("Inserting default user...");
    await db.execute(`
      INSERT INTO users (email, name) 
      VALUES ('<EMAIL>', 'Admin User')
      ON CONFLICT (email) DO NOTHING;
    `);

    // Insert sample employees
    console.log("Inserting sample employees...");
    await db.execute(`
      INSERT INTO employees (name, email, role, image_url) VALUES
      ('Alice Johnson', '<EMAIL>', 'CEO', 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=400&h=400&fit=crop&crop=face'),
      ('Bob Williams', '<EMAIL>', 'CTO', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face'),
      ('Charlie Brown', '<EMAIL>', 'Lead Developer', 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face')
      ON CONFLICT DO NOTHING;
    `);

    // Insert sample speakers
    console.log("Inserting sample speakers...");
    await db.execute(`
      INSERT INTO speakers (name, bio, category, image, rate, email, phone, location, experience, specialties, availability) VALUES
      ('Dr. Sarah Chen', 'AI researcher and tech entrepreneur with 15+ years in machine learning and data science.', 'Technology', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face', 25000, '<EMAIL>', '+****************', 'San Francisco, CA', '15+ years in AI and Machine Learning', '{"Artificial Intelligence", "Machine Learning", "Data Science", "Tech Innovation"}', 'Available'),
      ('Marcus Rodriguez', 'Former Google VP of Engineering, now startup advisor and cybersecurity expert.', 'Technology', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face', 30000, '<EMAIL>', '+****************', 'Austin, TX', '20+ years in Technology Leadership', '{"Cybersecurity", "Cloud Computing", "Digital Transformation", "Tech Leadership"}', 'Available'),
      ('Lisa Park', 'Blockchain pioneer and cryptocurrency expert, founder of three successful fintech startups.', 'Technology', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face', 22000, '<EMAIL>', '+****************', 'New York, NY', '12+ years in Blockchain and Fintech', '{"Blockchain", "Cryptocurrency", "Fintech", "Digital Innovation"}', 'Busy')
      ON CONFLICT DO NOTHING;
    `);

    console.log("Database migration completed successfully!");
  } catch (error) {
    console.error("Migration failed:", error);
    throw error;
  }
}
