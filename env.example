# Database Configuration
# Copy this file to .env and fill in your actual database credentials
VITE_DATABASE_URL="postgresql://username:password@host:port/database"

# Example for Neon DB:
# VITE_DATABASE_URL="postgresql://your-username:<EMAIL>/your-database"

# Example for local PostgreSQL:
# VITE_DATABASE_URL="postgresql://postgres:password@localhost:5432/mena_sales_dashboard"

# NextAuth Configuration (if needed later)
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:5173"

# Important Notes:
# 1. The VITE_ prefix is required for <PERSON><PERSON> to expose the variable to the browser
# 2. Never commit your .env file to version control
# 3. After creating the .env file, restart your development server
# 4. Run 'npm run db:migrate' to set up your database schema
