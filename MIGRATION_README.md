# Migration Guide: Supabase to Drizzle ORM + Neon DB

This document outlines the complete migration from Supabase to Drizzle ORM with Neon DB and custom authentication.

## Overview

The application has been migrated from:

- **Supabase** → **Drizzle ORM + Neon DB**
- **Supabase Auth** → **Custom Authentication Service**
- **Supabase Client** → **Drizzle Services**

## What Changed

### 1. Database Layer

- **Before**: Supabase client with direct SQL queries
- **After**: Drizzle ORM with typed schema and services

### 2. Authentication

- **Before**: Supabase Auth with session management
- **After**: Custom authentication service with localStorage-based sessions

### 3. Data Access

- **Before**: Direct Supabase queries in components
- **After**: Service layer with typed interfaces

## New File Structure

```
src/
├── db/
│   ├── index.ts          # Database connection
│   ├── schema.ts         # Drizzle schema definitions
│   └── migrate.ts        # Migration script
├── lib/
│   ├── auth-service.ts   # Custom authentication
│   └── services/         # Data access services
│       ├── speakers.ts
│       ├── events.ts
│       ├── proposals.ts
│       ├── employees.ts
│       ├── proposal-templates.ts
│       ├── categories.ts
│       ├── specialties.ts
│       ├── bitrix-deals.ts
│       ├── email-settings.ts
│       ├── speaker-images.ts
│       └── index.ts
└── contexts/
    └── AuthContext.tsx   # Updated auth context
```

## Setup Instructions

### 1. Install Dependencies

```bash
npm install drizzle-orm @neondatabase/serverless next-auth @auth/core @auth/drizzle-adapter
npm install -D drizzle-kit @types/pg tsx
```

### 2. Environment Configuration

Create a `.env` file with:

```env
DATABASE_URL="postgresql://username:password@host:port/database"
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:5173"
```

### 3. Database Migration

```bash
# Run the initial migration
npm run db:migrate

# Generate Drizzle migrations (for future changes)
npm run db:generate

# Push schema changes to database
npm run db:push
```

## Migration Steps

### Step 1: Update Components

Replace Supabase imports with service imports:

**Before:**

```tsx
import { supabase } from "@/integrations/supabase/client";

const { data, error } = await supabase.from("speakers").select("*");
```

**After:**

```tsx
import { speakersService } from "@/lib/services";

const speakers = await speakersService.getAll();
```

### Step 2: Update Authentication

Replace Supabase auth with custom auth service:

**Before:**

```tsx
const { user } = useAuth();
const {
  data: { session },
} = await supabase.auth.getSession();
```

**After:**

```tsx
const { user } = useAuth();
const session = await authService.getSession();
```

### Step 3: Update Data Types

Use the new typed interfaces from services:

**Before:**

```tsx
const speakers = data as any[];
```

**After:**

```tsx
import { Speaker } from "@/lib/services";
const speakers: Speaker[] = await speakersService.getAll();
```

## Service Layer

### Speakers Service

```tsx
import { speakersService } from "@/lib/services";

// Get all speakers
const speakers = await speakersService.getAll();

// Get speaker by ID
const speaker = await speakersService.getById(id);

// Create speaker
const newSpeaker = await speakersService.create(speakerData);

// Update speaker
const updatedSpeaker = await speakersService.update({ id, ...data });

// Delete speaker
await speakersService.delete(id);
```

### Proposals Service

```tsx
import { proposalsService } from "@/lib/services";

// Get all proposals
const proposals = await proposalsService.getAll();

// Get proposal with speaker
const proposal = await proposalsService.getWithSpeaker(id);
```

## Authentication Flow

### Login

```tsx
const { signIn } = useAuth();

try {
  await signIn(email, password);
  // Redirect on success
} catch (error) {
  // Handle error
}
```

### Logout

```tsx
const { signOut } = useAuth();

try {
  await signOut();
  // Clear local state
} catch (error) {
  // Handle error
}
```

### Session Management

```tsx
const { user, isLoading } = useAuth();

if (isLoading) {
  return <LoadingSpinner />;
}

if (!user) {
  return <LoginPage />;
}

return <ProtectedContent />;
```

## Database Schema

The new schema includes the following tables:

- `users` - User accounts
- `accounts` - OAuth accounts (for future use)
- `sessions` - User sessions
- `verificationTokens` - Email verification
- `employees` - Company employees
- `speakers` - Event speakers
- `proposals` - Event proposals
- `proposal_templates` - Customizable templates
- `categories` - Speaker categories
- `specialties` - Speaker specialties
- `speaker_images` - Speaker gallery

**Note**: The following tables have been removed:

- `events` - Scheduled events (removed)
- `bitrix_deals` - CRM integration (removed)
- `email_settings` - Email configuration (removed)

## Benefits of Migration

1. **Type Safety**: Full TypeScript support with Drizzle
2. **Performance**: Direct database connections without Supabase overhead
3. **Flexibility**: Custom authentication and business logic
4. **Cost**: Potentially lower costs with Neon DB
5. **Control**: Full control over database operations and schema

## Troubleshooting

### Common Issues

1. **Database Connection Error**

   - Verify `DATABASE_URL` in `.env`
   - Check Neon DB credentials and network access

2. **Migration Failures**

   - Ensure database is accessible
   - Check for existing tables that might conflict

3. **Type Errors**
   - Verify service imports are correct
   - Check that component props match service interfaces

### Debug Commands

```bash
# Check database connection
npm run db:migrate

# View generated SQL
npm run db:generate

# Push schema changes
npm run db:push
```

## Next Steps

1. **Complete Component Migration**: Update remaining components to use services
2. **Add Error Handling**: Implement comprehensive error handling in services
3. **Add Validation**: Add input validation using Zod or similar
4. **Performance Optimization**: Add caching and query optimization
5. **Testing**: Add unit tests for services and integration tests

## Support

For migration issues:

1. Check the service layer implementation
2. Verify database schema matches expectations
3. Review component error handling
4. Check browser console for detailed error messages
