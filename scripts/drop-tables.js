// Simple script to drop the tables
// Run this with: node scripts/drop-tables.js

const { neon } = require("@neondatabase/serverless");

async function dropTables() {
  try {
    // Get database URL from environment
    const databaseUrl =
      process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;

    if (!databaseUrl) {
      console.error(
        "❌ DATABASE_URL not found. Please set VITE_DATABASE_URL or DATABASE_URL environment variable."
      );
      console.log("\nExample:");
      console.log(
        'export VITE_DATABASE_URL="postgresql://username:<EMAIL>/database"'
      );
      return;
    }

    console.log("🔌 Connecting to database...");
    const sql = neon(databaseUrl);

    console.log("🗑️  Starting table removal...");

    // Drop tables in order (respecting foreign key dependencies)

    // Drop bitrix_deals table first (it references events)
    console.log("📋 Dropping bitrix_deals table...");
    await sql`DROP TABLE IF EXISTS bitrix_deals CASCADE`;

    // Drop events table
    console.log("📅 Dropping events table...");
    await sql`DROP TABLE IF EXISTS events CASCADE`;

    // Drop email_settings table
    console.log("📧 Dropping email_settings table...");
    await sql`DROP TABLE IF EXISTS email_settings CASCADE`;

    // Drop the birthday function since it's no longer needed
    console.log("🎂 Dropping get_todays_birthdays function...");
    await sql`DROP FUNCTION IF EXISTS get_todays_birthdays() CASCADE`;

    console.log("✅ All tables and functions dropped successfully!");
  } catch (error) {
    console.error("❌ Error dropping tables:", error);
    throw error;
  }
}

// Run the script
dropTables()
  .then(() => {
    console.log("🎉 Script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Script failed:", error);
    process.exit(1);
  });

