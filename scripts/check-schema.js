const { neon } = require("@neondatabase/serverless");
const { config } = require('dotenv');

// Load environment variables
config({ path: '.env.local' });

async function checkSchema() {
  try {
    const databaseUrl = process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;
    
    if (!databaseUrl) {
      console.error("❌ DATABASE_URL not found");
      return;
    }

    console.log("🔌 Connecting to database...");
    const sql = neon(databaseUrl);

    console.log("📋 Checking speakers table schema...");
    const result = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'speakers' 
      ORDER BY ordinal_position;
    `;

    console.log("Speakers table columns:");
    console.table(result);

    // Check if table exists
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'speakers'
      );
    `;
    
    console.log("Table exists:", tableExists[0].exists);

  } catch (error) {
    console.error("❌ Error checking schema:", error);
  }
}

checkSchema();
