import { neon } from "@neondatabase/serverless";
import { config } from "dotenv";

// Load environment variables
config({ path: ".env.local" });

async function checkSchema() {
  try {
    const databaseUrl =
      process.env.VITE_DATABASE_URL || process.env.DATABASE_URL;

    if (!databaseUrl) {
      console.error("❌ DATABASE_URL not found");
      console.log(
        "Available env vars:",
        Object.keys(process.env).filter((key) => key.includes("DATABASE"))
      );
      console.log("VITE_DATABASE_URL:", process.env.VITE_DATABASE_URL);
      return;
    }

    console.log("✅ Using database URL:", databaseUrl.substring(0, 50) + "...");

    console.log("🔌 Connecting to database...");
    const sql = neon(databaseUrl);

    // Check all tables
    console.log("📋 Checking all tables...");
    const tables = await sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;

    console.log("Existing tables:");
    console.table(tables);

    console.log("\n📋 Checking speakers table schema...");
    const result = await sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'speakers'
      ORDER BY ordinal_position;
    `;

    console.log("Speakers table columns:");
    console.table(result);

    // Check if table exists
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'speakers'
      );
    `;

    console.log("Table exists:", tableExists[0].exists);

    // Try to query speakers table to see if it works
    console.log("\n🔍 Testing speakers query...");
    try {
      const speakers = await sql`SELECT COUNT(*) as count FROM speakers;`;
      console.log("Speakers count:", speakers[0].count);
    } catch (queryError) {
      console.error("❌ Error querying speakers:", queryError.message);
    }
  } catch (error) {
    console.error("❌ Error checking schema:", error);
  }
}

checkSchema();
