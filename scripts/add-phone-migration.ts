import { config } from 'dotenv';
import { addPhoneToEmployees } from "../src/db/add-phone-migration";

// Load environment variables from .env file
config();

async function main() {
    try {
        await addPhoneToEmployees();
        console.log("Phone column migration completed successfully!");
        process.exit(0);
    } catch (error) {
        console.error("Phone column migration failed:", error);
        process.exit(1);
    }
}

main();
